//
//  iPadLayoutConfig.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/2.
//

import SwiftUI

/**
 * iPad专用布局配置
 * 为iPad设备提供优化的布局参数
 */
struct iPadLayoutConfig {
    
    // MARK: - 订阅页面iPad布局配置
    struct SubscriptionPage {
        
        // MARK: - 用户信息区域iPad配置
        struct UserInfoSection {
            static let heightPercentage: CGFloat = 0.33 // iPad减小高度占比
            static let topPositionPercentage: CGFloat = 0.18 // iPad顶部位置调整
            static let horizontalPadding: CGFloat = 80 // iPad增大左右边距
            static let verticalPadding: CGFloat = 35 // iPad增大上下边距
            static let cornerRadius: CGFloat = 20 // iPad增大圆角
            static let titleFontSize: CGFloat = 28 // iPad增大标题字体
            static let subtitleFontSize: CGFloat = 18 // iPad增大副标题字体
            static let iconSize: CGFloat = 50 // iPad增大图标尺寸
            static let backgroundColor = Color(hex: "#edf6d9")
            static let shadowRadius: CGFloat = 12 // iPad增大阴影
            static let shadowOpacity: Double = 0.15 // iPad调整阴影透明度
        }
        
        // MARK: - 分段选项卡iPad配置
        struct MembershipTab {
            static let topOffsetPercentage: CGFloat = 0.30 // iPad调整顶部偏移 - 修改为屏幕高度的28%
            static let offsetX: CGFloat = 40
            static let offsetY: CGFloat = 0
            static let tabWidth: CGFloat = 150 // iPad增大选项卡宽度
            static let tabHeight: CGFloat = 55 // iPad增大选项卡高度
            static let totalWidth: CGFloat = 350 // iPad增大宽度
            static let scaleX: CGFloat = 3.0 // iPad恢复正常比例
            static let scaleY: CGFloat = 2.2 // iPad恢复正常比例
            static let spacing: CGFloat = 10 // iPad增大间距
            static let horizontalPadding: CGFloat = 100 // iPad增大左右边距
            static let tabCornerRadius: CGFloat = 22 // iPad增大圆角
            static let textFontSize: CGFloat = 18 // iPad增大文字
            static let selectedTextOpacity: Double = 0 // iPad恢复文字透明度
            static let unselectedTextOpacity: Double = 0 // iPad调整未选中文字透明度
            static let selectedBackgroundOpacity: Double = 0.0 // iPad保持透明背景
            static let unselectedBackgroundOpacity: Double = 0.0 // iPad保持透明背景
            static let shadowRadius: CGFloat = 0 // iPad移除阴影
            static let shadowOpacity: Double = 0.0 // iPad移除阴影透明度
            static let animationDuration: Double = 0.3 // iPad动画时长
            static let animationSpringResponse: Double = 0.5 // iPad弹簧响应
            static let animationSpringDamping: Double = 0.7 // iPad弹簧阻尼
            static let pressedScale: CGFloat = 0.95 // iPad按下缩放
            static let hoverScale: CGFloat = 1.02 // iPad悬停缩放
            static let touchAreaPadding: CGFloat = 10 // iPad扩大点击区域
            static let touchAreaMinHeight: CGFloat = 60 // iPad最小点击高度
            static let touchAreaMinWidth: CGFloat = 120 // iPad最小点击宽度
        }
        
        // MARK: - 内容区域iPad配置
        struct ContentSection {
            static let topOffsetPercentage: CGFloat = 0.58 // iPad内容区域顶部偏移
            static let horizontalPadding: CGFloat = 60 // iPad增大左右边距
            static let verticalSpacing: CGFloat = 30 // iPad增大垂直间距
            static let featureToPriceSpacing: CGFloat = 40 // iPad权益到价格间距
            static let priceToButtonSpacing: CGFloat = 40 // iPad价格到按钮间距
            static let buttonToAgreementSpacing: CGFloat = 35 // iPad按钮到协议间距
            static let maxContentWidth: CGFloat = 600 // iPad限制内容最大宽度
        }
        
        // MARK: - 功能列表iPad配置
        struct FeatureList {
            static let iconSize: CGFloat = 42 // iPad增大图标
            static let fontSize: CGFloat = 18 // iPad增大字体
            static let spacing: CGFloat = 16 // iPad增大间距
            static let verticalSpacing: CGFloat = 8 // iPad增大垂直间距
        }
        
        // MARK: - 价格卡片iPad配置
        struct PriceCard {
            static let width: CGFloat = 180 // iPad增大卡片宽度
            static let height: CGFloat = 180 // iPad增大卡片高度
            static let cornerRadius: CGFloat = 20 // iPad增大圆角
            static let spacing: CGFloat = 20 // iPad增大间距
            static let titleFont: CGFloat = 30 // iPad增大标题字体
            static let priceFont: CGFloat = 56 // iPad增大价格字体
            static let unitFont: CGFloat = 28 // iPad增大单位字体
            static let selectedBorderWidth: CGFloat = 12 // iPad增大选中边框
            static let unselectedBorderWidth: CGFloat = 4 // iPad增大未选中边框
        }
        
        // MARK: - 订阅按钮iPad配置
        struct SubscribeButton {
            static let height: CGFloat = 60 // iPad增大按钮高度
            static let cornerRadius: CGFloat = 30 // iPad增大圆角
            static let fontSize: CGFloat = 22 // iPad增大字体
            static let horizontalPadding: CGFloat = 60 // iPad增大左右边距
            static let maxWidth: CGFloat = 400 // iPad限制最大宽度
            static let shadowRadius: CGFloat = 10 // iPad增大阴影
            static let shadowOpacity: Double = 0.2 // iPad调整阴影透明度
        }
        
        // MARK: - 协议区域iPad配置
        struct AgreementSection {
            static let fontSize: CGFloat = 16 // iPad增大字体
            static let checkboxSize: CGFloat = 20 // iPad增大复选框
            static let spacing: CGFloat = 20 // iPad增大间距
            static let horizontalPadding: CGFloat = 40 // iPad增大左右边距
            static let maxWidth: CGFloat = 450 // iPad限制最大宽度
        }
        
        // MARK: - 背景图片iPad配置
        struct BackgroundImage {
            static let offsetX: CGFloat = 0 // iPad背景图水平偏移
            static let offsetY: CGFloat = 20 // iPad背景图垂直偏移
            static let scaleX: CGFloat = 1.1 // iPad背景图水平缩放
            static let scaleY: CGFloat = 1.1 // iPad背景图垂直缩放
            static let opacity: Double = 1.0 // iPad背景图透明度
            static let blur: CGFloat = 0.0 // iPad背景图模糊
        }
    }
    
    // MARK: - 通用iPad配置
    struct General {
        static let containerMaxWidth: CGFloat = 800 // iPad容器最大宽度
        static let sideMargin: CGFloat = 60 // iPad侧边距
        static let contentPadding: CGFloat = 40 // iPad内容边距
        static let sectionSpacing: CGFloat = 40 // iPad区块间距
    }
}

/**
 * SwiftUI View扩展
 * 提供iPad布局配置的便捷方法
 */
extension View {
    /// 应用iPad优化布局
    func applyiPadLayout() -> some View {
        self.modifier(iPadLayoutModifier())
    }
    
    /// 应用iPad订阅页面布局
    func applyiPadSubscriptionLayout() -> some View {
        self.modifier(iPadSubscriptionLayoutModifier())
    }
}

/**
 * iPad布局修饰符
 */
struct iPadLayoutModifier: ViewModifier {
    func body(content: Content) -> some View {
        content
            .frame(maxWidth: DeviceDetection.isPad ? iPadLayoutConfig.General.containerMaxWidth : .infinity)
            .padding(.horizontal, DeviceDetection.isPad ? iPadLayoutConfig.General.sideMargin : 25)
    }
}

/**
 * iPad订阅页面布局修饰符
 */
struct iPadSubscriptionLayoutModifier: ViewModifier {
    func body(content: Content) -> some View {
        content
            .frame(maxWidth: DeviceDetection.isPad ? iPadLayoutConfig.SubscriptionPage.ContentSection.maxContentWidth : .infinity)
            .padding(.horizontal, DeviceDetection.isPad ? iPadLayoutConfig.SubscriptionPage.ContentSection.horizontalPadding : 25)
    }
}
