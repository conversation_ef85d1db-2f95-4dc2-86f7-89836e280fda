//
//  MembershipTabSegment.swift
//  ztt2
//
//  Created by AI Assistant on 2025/8/2.
//

import SwiftUI

/**
 * 会员等级切换分段选项卡组件
 * 透明背景设计，与背景图完美重合
 * 支持平滑的切换动画
 */
struct MembershipTabSegment: View {
    
    // MARK: - Properties
    @Binding var selectedTab: Int // 0: 初级会员, 1: 高级会员
    let onTabChanged: (Int) -> Void
    
    // MARK: - Computed Properties
    private var tabTitles: [String] {
        return [
            "subscription.membership.basic".localized,
            "subscription.membership.premium".localized
        ]
    }
    
    // MARK: - State
    @State private var tabAppeared = false
    @State private var buttonPressedStates = [false, false]
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 透明背景容器
                RoundedRectangle(cornerRadius: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.tabCornerRadius)
                    .fill(Color.clear) // 完全透明，让背景图透过
                    .frame(
                        width: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.totalWidth,
                        height: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.tabHeight
                    )
                
                // 分段选项卡按钮
                HStack(spacing: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.spacing) {
                    ForEach(0..<tabTitles.count, id: \.self) { index in
                        createTabButton(title: tabTitles[index], index: index, geometry: geometry)
                    }
                }
                .frame(height: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.tabHeight)
            }
            .scaleEffect(
                x: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.scaleX,
                y: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.scaleY
            )
            .offset(
                x: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.offsetX,
                y: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.offsetY
            )
            .opacity(tabAppeared ? 1.0 : 0.0)
            .scaleEffect(tabAppeared ? 1.0 : DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.pressedScale)
            .animation(
                .spring(
                    response: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.animationSpringResponse,
                    dampingFraction: DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.animationSpringDamping
                ).delay(0.3),
                value: tabAppeared
            )
        }
        .onAppear {
            withAnimation {
                tabAppeared = true
            }
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 创建分段选项卡按钮
     * 📌 新增：扩大点击区域，提升用户体验
     */
    private func createTabButton(title: String, index: Int, geometry: GeometryProxy) -> some View {
        let isSelected = selectedTab == index
        let config = DesignSystem.AdaptiveLayout.SubscriptionPage.MembershipTab.self
        
        // 如果设置了自定义宽度，使用它，否则平均分配
        let buttonWidth = config.tabWidth > 0 ? config.tabWidth : geometry.size.width / CGFloat(tabTitles.count)
        
        // 计算扩展的点击区域尺寸
        let touchWidth = max(buttonWidth + config.touchAreaPadding * 2, config.touchAreaMinWidth)
        let touchHeight = max(config.tabHeight + config.touchAreaPadding * 2, config.touchAreaMinHeight)
        
        return Button(action: {
            handleTabSelection(index: index)
        }) {
            ZStack {
                // 背景 - 完全透明，让背景图透过
                RoundedRectangle(cornerRadius: config.tabCornerRadius)
                    .fill(Color.clear)
                    .frame(width: buttonWidth, height: config.tabHeight)
                    .overlay(
                        RoundedRectangle(cornerRadius: config.tabCornerRadius)
                            .stroke(Color.clear, lineWidth: 0) // 移除边框
                    )
                
                // 文字 - 完全透明，让背景图上的文字显示
                Text(title)
                    .font(.system(
                        size: config.textFontSize,
                        weight: DesignSystem.SubscriptionPage.MembershipTab.fontWeight
                    ))
                    .foregroundColor(Color.clear) // 完全透明
                    .opacity(isSelected ? config.selectedTextOpacity : config.unselectedTextOpacity)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .frame(width: touchWidth, height: touchHeight) // 扩大的点击区域
        .contentShape(Rectangle()) // 确保整个区域都可点击
        .scaleEffect(buttonPressedStates[index] ? config.pressedScale : 1.0)
        .animation(
            .easeInOut(duration: config.animationDuration),
            value: buttonPressedStates[index]
        )
        .onTapGesture {
            // 触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
            
            // 按下效果
            withAnimation(.easeInOut(duration: 0.1)) {
                buttonPressedStates[index] = true
            }
            
            // 恢复效果
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    buttonPressedStates[index] = false
                }
            }
            
            // 处理选择
            handleTabSelection(index: index)
        }
    }
    
    /**
     * 处理选项卡选择
     */
    private func handleTabSelection(index: Int) {
        guard selectedTab != index else { return }
        
        // 更新选中状态
        selectedTab = index
        
        // 调用回调
        onTabChanged(index)
        
        // 打印调试信息
        let membershipType = index == 0 ? "初级会员" : "高级会员"
        print("🔄 分段选项卡切换到: \(membershipType)")
    }
}

// MARK: - Preview
#Preview {
    struct PreviewWrapper: View {
        @State var selectedTab = 1
        
        var body: some View {
            ZStack {
                // 模拟背景图
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(hex: "#e7f7c4"),
                        Color(hex: "#d4f1a8")
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack(spacing: 40) {
                    Spacer()
                    
                    // 分段选项卡
                    MembershipTabSegment(selectedTab: $selectedTab) { newTab in
                        print("切换到: \(newTab == 0 ? "初级会员" : "高级会员")")
                    }
                    .frame(height: 44)
                    .padding(.horizontal, 60)
                    
                    // 显示当前选中状态
                    Text("tab.current_selection".localized(with: selectedTab == 0 ? "subscription.membership.basic".localized : "subscription.membership.premium".localized))
                        .font(.title2)
                        .foregroundColor(.primary)
                    
                    Spacer()
                }
            }
        }
    }
    
    return PreviewWrapper()
}
